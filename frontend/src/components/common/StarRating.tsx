import React, { useState } from 'react';
import { Star } from 'lucide-react';
import { cn } from '../../lib/utils.ts';

interface StarRatingProps {
  rating: number;
  onRatingChange?: (rating: number) => void;
  maxRating?: number;
  size?: 'sm' | 'md' | 'lg';
  readonly?: boolean;
  showValue?: boolean;
  className?: string;
}

const starSizes = {
  sm: 'h-4 w-4',
  md: 'h-5 w-5',
  lg: 'h-6 w-6',
};

export default function StarRating({
  rating,
  onRatingChange,
  maxRating = 5,
  size = 'md',
  readonly = false,
  showValue = false,
  className,
}: StarRatingProps) {
  const [hoverRating, setHoverRating] = useState(0);

  const handleStarClick = (starValue: number) => {
    if (!readonly && onRatingChange) {
      onRatingChange(starValue);

      // Trigger pop animation
      const starElement = document.querySelector(`[data-star="${starValue}"]`);
      if (starElement) {
        starElement.classList.add('animate-star-pop');
        setTimeout(() => {
          starElement.classList.remove('animate-star-pop');
        }, 300);
      }
    }
  };

  const handleStarHover = (starValue: number) => {
    if (!readonly) {
      setHoverRating(starValue);
    }
  };

  const handleMouseLeave = () => {
    if (!readonly) {
      setHoverRating(0);
    }
  };

  const displayRating = hoverRating || rating;

  return (
    <div className={cn('flex items-center gap-1', className)}>
      <div 
        className="flex items-center"
        onMouseLeave={handleMouseLeave}
      >
        {Array.from({ length: maxRating }, (_, index) => {
          const starValue = index + 1;
          const isFilled = starValue <= displayRating;
          const isPartiallyFilled = !Number.isInteger(displayRating) && 
            starValue === Math.ceil(displayRating) && 
            starValue > displayRating;

          return (
            <button
              key={index}
              type="button"
              data-star={starValue}
              className={cn(
                'relative transition-all duration-200',
                !readonly && 'hover:scale-110 cursor-pointer',
                readonly && 'cursor-default'
              )}
              onClick={() => handleStarClick(starValue)}
              onMouseEnter={() => handleStarHover(starValue)}
              disabled={readonly}
            >
              {/* Background star (empty) */}
              <Star
                className={cn(
                  starSizes[size],
                  'text-gray-300'
                )}
                fill="currentColor"
              />

              {/* Filled star overlay */}
              {(isFilled || isPartiallyFilled) && (
                <Star
                  className={cn(
                    starSizes[size],
                    'absolute inset-0 text-secondary-500'
                  )}
                  fill="currentColor"
                  style={{
                    clipPath: isPartiallyFilled
                      ? `inset(0 ${100 - ((displayRating % 1) * 100)}% 0 0)`
                      : undefined
                  }}
                />
              )}
            </button>
          );
        })}
      </div>

      {showValue && (
        <span className="ml-2 text-sm text-gray-600">
          {rating.toFixed(1)} / {maxRating}
        </span>
      )}
    </div>
  );
}

// Compact star rating display for lists
export function CompactStarRating({ 
  rating, 
  reviewCount 
}: { 
  rating: number; 
  reviewCount?: number; 
}) {
  return (
    <div className="flex items-center gap-1">
      <StarRating rating={rating} size="sm" readonly />
      <span className="text-sm text-gray-600">
        {rating.toFixed(1)}
        {reviewCount !== undefined && ` (${reviewCount})`}
      </span>
    </div>
  );
}

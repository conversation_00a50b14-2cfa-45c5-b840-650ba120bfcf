import { defineConfig } from 'astro/config';
import tailwind from '@astrojs/tailwind';
import alpinejs from '@astrojs/alpinejs';
import react from '@astrojs/react';
import node from '@astrojs/node';
import path from 'node:path';
import { fileURLToPath } from 'node:url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// https://astro.build/config
export default defineConfig({
  output: 'server',
  adapter: node({
    mode: 'standalone'
  }),
  integrations: [
    tailwind(),
    alpinejs(),
    react({
      include: ['**/react/*', '**/*.tsx', '**/*.jsx'],
      experimentalReactChildren: true
    })
  ],
  vite: {
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src'),
        '@/components': path.resolve(__dirname, 'src/components'),
        '@/layouts': path.resolve(__dirname, 'src/layouts'),
        '@/lib': path.resolve(__dirname, 'src/lib'),
        '@/styles': path.resolve(__dirname, 'src/styles'),
        '@/content': path.resolve(__dirname, 'src/content'),
        '@/pages': path.resolve(__dirname, 'src/pages'),
        '@/TextAnimations': path.resolve(__dirname, 'src/TextAnimations'),
      }
    },
    esbuild: {
      jsx: 'automatic',
      jsxImportSource: 'react'
    }
  }
});

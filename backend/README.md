# Deno Venue Services

Backend services for the Trodoo venue booking platform built with Deno.

## Overview

This service handles all asynchronous tasks for the Trodoo platform including:

- **Search Sync Service**: Real-time synchronization between PocketBase venues and MeiliSearch
- **Notification Service**: Automated emails for bookings, reminders, and lifecycle events
- **Payment Webhook Service**: Secure Paystack webhook handling for payment confirmations
- **Payout Service**: Scheduled processing of venue owner payouts

## Architecture

```
/
├── src/
│   ├── services/
│   │   ├── emailService.ts          // Email provider wrapper (Resend)
│   │   ├── meilisearchSyncService.ts // PocketBase -> MeiliSearch sync
│   │   ├── notificationService.ts   // Central notification hub
│   │   ├── paystackWebhookService.ts // Payment webhook handling
│   │   └── payoutService.ts         // Payout processing
│   ├── types/
│   │   ├── paystack.ts             // Paystack API types
│   │   └── pocketbase.ts           // PocketBase types
│   └── utils/
│       └── logger.ts               // Logging utility
├── main.ts                         // Main server entry point
├── deps.ts                         // Centralized dependencies
├── Dockerfile                      // Container configuration
└── .env.example                    // Environment variables template
```

## Features

### Real-time Search Synchronization
- Listens to PocketBase venue collection changes
- Updates MeiliSearch index within seconds
- Handles create, update, and delete operations

### Comprehensive Notification System
- **Transactional Emails**: Welcome, booking confirmations, payment alerts
- **Scheduled Reminders**: Check-in/checkout alerts, review prompts
- **Event Invitations**: Guest invitation system for bookings
- **Duplicate Prevention**: Tracks sent notifications to prevent spam

### Secure Payment Processing
- Webhook signature verification
- Idempotent payment processing
- Automatic booking status updates
- Comprehensive error handling and logging

### Automated Payouts
- Daily processing of completed bookings
- Paystack transfers API integration
- Automatic status tracking and updates

## Getting Started

### Prerequisites

- Deno 1.40+
- PocketBase instance (or use the provided PocketHost URL)
- MeiliSearch instance (optional, can use Docker)
- Paystack account
- Email service (Resend) account

### Quick Start with Docker

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd deno-venue-services
   ```

2. Copy environment variables:
   ```bash
   cp .env.example .env
   ```

3. Configure required environment variables in `.env`:
   ```bash
   # Required
   POCKETBASE_URL=https://trodoorentals.pockethost.io
   POCKETBASE_ADMIN_EMAIL=your_admin_email
   POCKETBASE_ADMIN_PASSWORD=your_admin_password
   RESEND_API_KEY=re_your_resend_api_key
   PAYSTACK_SECRET_KEY=sk_test_your_paystack_secret_key
   PAYSTACK_WEBHOOK_SECRET=your_webhook_secret
   ```

4. Start all services:
   ```bash
   docker-compose up -d
   ```

5. Test the setup:
   ```bash
   deno run --allow-net --allow-env --allow-read test.ts
   ```

### Manual Installation

1. Install Deno:
   ```bash
   curl -fsSL https://deno.land/install.sh | sh
   ```

2. Configure environment variables in `.env`

3. Run the service:
   ```bash
   deno run --allow-net --allow-env --allow-read main.ts
   ```

### Development

```bash
# Run with file watching
deno run --allow-net --allow-env --allow-read --watch main.ts

# Run tests
deno run --allow-net --allow-env --allow-read test.ts

# Format code
deno fmt

# Lint code
deno lint

# Check dependencies
deno info deps.ts
```

## Environment Variables

See `.env.example` for all required environment variables.

### Required Variables

```bash
# PocketBase Configuration
POCKETBASE_URL=https://trodoorentals.pockethost.io
POCKETBASE_ADMIN_EMAIL=<EMAIL>
POCKETBASE_ADMIN_PASSWORD=your_secure_password

# Email Service (Resend)
RESEND_API_KEY=re_your_resend_api_key
FROM_EMAIL=<EMAIL>
FROM_NAME=Trodoo

# Paystack Configuration
PAYSTACK_SECRET_KEY=sk_test_your_paystack_secret_key
PAYSTACK_PUBLIC_KEY=pk_test_your_paystack_public_key
PAYSTACK_WEBHOOK_SECRET=your_webhook_secret

# Frontend URL for email links
FRONTEND_URL=https://trodoorentals.up.railway.app
```

### Optional Variables

```bash
# MeiliSearch (for enhanced search)
MEILISEARCH_HOST=http://localhost:7700
MEILISEARCH_API_KEY=masterKey

# Logging and Performance
LOG_LEVEL=INFO
ENABLE_CRON_JOBS=true
RATE_LIMIT_MAX_REQUESTS=100
```

## API Endpoints

- `GET /health` - Health check endpoint
- `GET /api/status` - Service status and uptime
- `POST /webhooks/paystack` - Paystack payment webhooks
- `POST /api/internal/send-invitations` - Guest invitation endpoint

## Testing

### Run Basic Tests

```bash
# Test environment and service health
deno run --allow-net --allow-env --allow-read test.ts

# Test specific endpoints
curl http://localhost:8000/health
curl http://localhost:8000/api/status
```

### Test Email Service

```bash
# Send test invitation (requires running service)
curl -X POST http://localhost:8000/api/internal/send-invitations \
  -H "Content-Type: application/json" \
  -d '{
    "bookingId": "test_booking_id",
    "message": "Test invitation message",
    "emails": ["<EMAIL>"]
  }'
```

### Test Webhook

```bash
# Test webhook endpoint (requires valid signature)
curl -X POST http://localhost:8000/webhooks/paystack \
  -H "Content-Type: application/json" \
  -H "x-paystack-signature: test_signature" \
  -d '{"event": "charge.success", "data": {...}}'
```

## Deployment

### Docker Deployment

```bash
# Build and run with Docker Compose
docker-compose up -d

# View logs
docker-compose logs -f venue-services

# Scale services
docker-compose up -d --scale venue-services=2
```

### Manual Deployment

```bash
# Build image
docker build -t deno-venue-services .

# Run container
docker run -p 8000:8000 --env-file .env deno-venue-services

# Run in background
docker run -d -p 8000:8000 --env-file .env --name venue-services deno-venue-services
```

### Production Considerations

- **Security**: Use environment variables for all secrets
- **Logging**: Configure proper logging levels and log aggregation
- **Monitoring**: Set up health checks and alerting
- **Rate Limiting**: Implement rate limiting for public endpoints
- **HTTPS**: Use HTTPS in production with proper SSL certificates
- **Database**: Ensure PocketBase has proper backups
- **Email**: Configure proper email templates and delivery monitoring
- **Webhooks**: Secure webhook endpoints with proper signature verification

## Contributing

1. Follow Deno conventions and formatting
2. Add tests for new features
3. Update documentation as needed
4. Ensure all environment variables are documented

## License

MIT License

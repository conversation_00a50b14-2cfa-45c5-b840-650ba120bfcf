#!/bin/bash

# Quick test to verify the Docker image can be pulled and basic commands work
echo "🐳 Testing Deno Docker Image Availability"
echo "========================================"

# Test if we can pull the image
echo "📥 Pulling denoland/deno:latest..."
if docker pull denoland/deno:latest; then
    echo "✅ Successfully pulled denoland/deno:latest"
    
    # Test basic Deno command
    echo "🔍 Testing Deno version..."
    if docker run --rm denoland/deno:latest deno --version; then
        echo "✅ Deno is working correctly"
        
        # Test if we can cache a simple dependency
        echo "📦 Testing dependency caching..."
        if docker run --rm -v "$(pwd)":/app -w /app denoland/deno:latest deno cache deps.ts; then
            echo "✅ Dependency caching works"
            echo ""
            echo "🎉 Docker image is ready for building!"
        else
            echo "❌ Dependency caching failed"
            exit 1
        fi
    else
        echo "❌ Deno command failed"
        exit 1
    fi
else
    echo "❌ Failed to pull Docker image"
    echo "This might be a network issue or the image tag doesn't exist"
    exit 1
fi

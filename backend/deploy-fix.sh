#!/bin/bash

echo "🚀 Deploying PocketBase connection fix..."

# Check if we're in a git repository
if [ ! -d ".git" ]; then
    echo "❌ Not in a git repository. Please run from the project root."
    exit 1
fi

# Add and commit changes
echo "📝 Committing changes..."
git add .
git commit -m "Fix PocketBase connection logic - remove overly restrictive placeholder checks

- Updated notification service to only consider exact localhost URLs as placeholders
- Fixed admin email check to only flag exact default email
- Added debug endpoint to help troubleshoot connection issues
- This should resolve 'PocketBase not connected' errors in production"

# Push to main branch (Railway auto-deploys)
echo "🔄 Pushing to main branch..."
git push origin main

echo "✅ Changes pushed! Railway should auto-deploy in a few minutes."
echo ""
echo "🧪 After deployment, test with:"
echo "   curl https://trodoorentalsbe-production.up.railway.app/api/internal/debug-env"
echo "   curl https://trodoorentalsbe-production.up.railway.app/api/internal/send-invitations \\"
echo "     -H 'Content-Type: application/json' \\"
echo "     -d '{\"bookingId\": \"rb0bkv3yrrrt6y5\", \"message\": \"Test\", \"emails\": [\"<EMAIL>\"]}'"

{"nodeModulesDir": "auto", "tasks": {"dev": "deno run --allow-net --allow-env --allow-read --watch main.ts", "start": "deno run --allow-net --allow-env --allow-read main.ts", "test": "deno run --allow-net --allow-env --allow-read test.ts", "health": "deno run --allow-net --allow-env --allow-read healthcheck.ts"}, "imports": {"@oak/oak": "https://deno.land/x/oak@v12.6.1/mod.ts", "@std/http/status": "https://deno.land/std@0.208.0/http/status.ts", "@std/http/server": "https://deno.land/std@0.208.0/http/server.ts", "@std/crypto": "https://deno.land/std@0.208.0/crypto/mod.ts", "@std/encoding/hex": "https://deno.land/std@0.208.0/encoding/hex.ts", "@std/encoding/base64": "https://deno.land/std@0.208.0/encoding/base64.ts", "@std/dotenv": "https://deno.land/std@0.208.0/dotenv/mod.ts", "@std/datetime": "https://deno.land/std@0.208.0/datetime/mod.ts", "@std/log": "https://deno.land/std@0.208.0/log/mod.ts", "@std/json": "https://deno.land/std@0.208.0/json/mod.ts", "@std/uuid": "https://deno.land/std@0.208.0/uuid/mod.ts", "@std/async/delay": "https://deno.land/std@0.208.0/async/delay.ts", "pocketbase": "npm:pocketbase@^0.26.0", "meilisearch": "npm:me<PERSON><PERSON>ch@0.35.0", "resend": "npm:resend@2.0.0", "zod": "https://deno.land/x/zod@v3.22.4/mod.ts", "cors": "https://deno.land/x/cors@v1.2.2/mod.ts", "rate-limiter-flexible": "npm:rate-limiter-flexible@7.0.0", "mjml": "npm:mjml@4.14.1", "deno-cron": "https://deno.land/x/deno_cron@v1.0.0/cron.ts"}, "compilerOptions": {"lib": ["deno.window"], "strict": true}, "fmt": {"useTabs": false, "lineWidth": 100, "indentWidth": 2, "semiColons": true, "singleQuote": false, "proseWrap": "preserve", "include": ["src/", "*.ts"], "exclude": ["node_modules/", "dist/"]}, "lint": {"include": ["src/", "*.ts"], "exclude": ["node_modules/", "dist/"], "rules": {"tags": ["recommended"]}}}
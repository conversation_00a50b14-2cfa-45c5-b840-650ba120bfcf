// Paystack webhook service for payment processing
import { PocketBase, crypto, hexEncode } from "../../deps.ts";
import { createLogger, PerformanceLogger } from "../utils/logger.ts";
import type { 
  PaystackWebhookPayload,
  PaystackChargeData,
  WebhookVerification
} from "../types/paystack.ts";
import type { Booking } from "../types/pocketbase.ts";

const logger = createLogger("PaystackWebhook");

export interface WebhookResult {
  success: boolean;
  error?: string;
  processed?: boolean;
}

class PaystackWebhookService {
  private pb: PocketBase | null = null;
  private webhookSecret: string;
  private isInitialized = false;

  constructor() {
    this.webhookSecret = Deno.env.get("PAYSTACK_WEBHOOK_SECRET") || "";
  }

  async initialize(): Promise<void> {
    const pbUrl = Deno.env.get("POCKETBASE_URL");
    const adminEmail = Deno.env.get("POCKETBASE_ADMIN_EMAIL");
    const adminPassword = Deno.env.get("POCKETBASE_ADMIN_PASSWORD");
    const nodeEnv = Deno.env.get("NODE_ENV") || "development";

    // Check if we have placeholder values or missing config
    const hasPlaceholderValues =
      !pbUrl || pbUrl === "http://localhost:8090" ||
      !adminEmail || adminEmail === "<EMAIL>" ||
      !adminPassword || adminPassword.includes("your_") ||
      !this.webhookSecret || this.webhookSecret.includes("your_");

    if (hasPlaceholderValues) {
      // Always use mock service for invalid/placeholder values
      logger.warn("Paystack webhook service running in mock mode (configuration not available)", {
        environment: nodeEnv,
        hasPbUrl: !!pbUrl,
        hasAdminEmail: !!adminEmail,
        hasAdminPassword: !!adminPassword,
        hasWebhookSecret: !!this.webhookSecret,
        isPlaceholder: hasPlaceholderValues
      });
      this.pb = null;
      this.isInitialized = true;
      return;
    }

    try {
      this.pb = new PocketBase(pbUrl);

      // Authenticate as admin
      await this.pb.admins.authWithPassword(adminEmail, adminPassword);

      this.isInitialized = true;
      logger.info("Paystack webhook service initialized");
    } catch (error) {
      // If PocketBase connection fails, fall back to mock mode
      logger.warn("Paystack webhook service falling back to mock mode due to connection error", {
        error: error instanceof Error ? error.message : String(error),
        pbUrl: pbUrl
      });
      this.pb = null;
      this.isInitialized = true;
    }
  }

  isHealthy(): boolean {
    return this.isInitialized;
  }

  async handleWebhook(body: string, signature: string): Promise<WebhookResult> {
    const perfLogger = new PerformanceLogger("handleWebhook");

    try {
      // Mock mode - just log the webhook
      if (!this.pb) {
        logger.info("Mock webhook received", { bodyLength: body.length, signature });
        perfLogger.end({ event: "mock", processed: true });
        return { success: true, processed: true };
      }

      // Verify webhook signature
      const verification = await this.verifySignature(body, signature);
      if (!verification.isValid) {
        perfLogger.endWithError(new Error("Invalid signature"));
        return { success: false, error: verification.error || "Invalid signature" };
      }

      // Parse webhook payload
      let payload: PaystackWebhookPayload;
      try {
        payload = JSON.parse(body);
      } catch (error) {
        perfLogger.endWithError(error);
        return { success: false, error: "Invalid JSON payload" };
      }

      // Process based on event type
      const result = await this.processWebhookEvent(payload);

      perfLogger.end({
        event: payload.event,
        processed: result.processed
      });

      return result;
    } catch (error) {
      perfLogger.endWithError(error);
      logger.error("Webhook processing failed", error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      return { success: false, error: errorMessage };
    }
  }

  private async verifySignature(body: string, signature: string): Promise<WebhookVerification> {
    try {
      // Create HMAC-SHA512 hash
      const key = await crypto.subtle.importKey(
        "raw",
        new TextEncoder().encode(this.webhookSecret),
        { name: "HMAC", hash: "SHA-512" },
        false,
        ["sign"]
      );

      const hashBuffer = await crypto.subtle.sign(
        "HMAC",
        key,
        new TextEncoder().encode(body)
      );

      const computedSignature = hexEncode(new Uint8Array(hashBuffer));
      
      // Compare signatures
      const isValid = computedSignature === signature;
      
      if (!isValid) {
        logger.warn("Webhook signature verification failed", {
          expected: signature,
          computed: computedSignature
        });
      }

      return { isValid };
    } catch (error) {
      logger.error("Signature verification error", error);
      return { isValid: false, error: error.message };
    }
  }

  private async processWebhookEvent(payload: PaystackWebhookPayload): Promise<WebhookResult> {
    logger.info("Processing webhook event", { event: payload.event });

    switch (payload.event) {
      case "charge.success":
        return await this.handleChargeSuccess(payload.data as PaystackChargeData);
      
      case "charge.failed":
        return await this.handleChargeFailed(payload.data as PaystackChargeData);
      
      case "transfer.success":
        return await this.handleTransferSuccess(payload.data);
      
      case "transfer.failed":
        return await this.handleTransferFailed(payload.data);
      
      default:
        logger.info("Unhandled webhook event", { event: payload.event });
        return { success: true, processed: false };
    }
  }

  private async handleChargeSuccess(chargeData: PaystackChargeData): Promise<WebhookResult> {
    try {
      const reference = chargeData.reference;
      const bookingId = chargeData.metadata?.booking_id;

      if (!bookingId) {
        logger.warn("No booking ID in charge metadata", { reference });
        return { success: true, processed: false };
      }

      // Find the booking
      const booking = await this.pb!.collection("bookings").getFirstListItem(
        `paystack_ref = "${reference}"`,
        { requestKey: null }
      ).catch(() => null) as Booking | null;

      if (!booking) {
        logger.warn("Booking not found for payment", { reference, bookingId });
        return { success: true, processed: false };
      }

      // Check for idempotency - if already paid, don't process again
      if (booking.status === "paid") {
        logger.info("Payment already processed", { bookingId, reference });
        return { success: true, processed: false };
      }

      // Verify payment amount matches booking
      const expectedAmount = Math.round(booking.total_price * 100); // Convert to kobo
      if (chargeData.amount !== expectedAmount) {
        logger.error("Payment amount mismatch", {
          bookingId,
          reference,
          expected: expectedAmount,
          received: chargeData.amount
        });
        return { success: false, error: "Payment amount mismatch" };
      }

      // Update booking status to paid
      await this.pb!.collection("bookings").update(booking.id, {
        status: "paid"
      });

      logger.info("Payment processed successfully", {
        bookingId: booking.id,
        reference,
        amount: chargeData.amount
      });

      return { success: true, processed: true };
    } catch (error) {
      logger.error("Failed to process charge success", error, {
        reference: chargeData.reference
      });
      return { success: false, error: error.message };
    }
  }

  private async handleChargeFailed(chargeData: PaystackChargeData): Promise<WebhookResult> {
    try {
      const reference = chargeData.reference;
      const bookingId = chargeData.metadata?.booking_id;

      logger.info("Payment failed", {
        reference,
        bookingId,
        message: chargeData.message,
        gatewayResponse: chargeData.gateway_response
      });

      // For failed payments, we might want to notify the user
      // but we don't need to update the booking status as it should remain "confirmed"
      // until a successful payment is made

      return { success: true, processed: true };
    } catch (error) {
      logger.error("Failed to process charge failure", error, {
        reference: chargeData.reference
      });
      return { success: false, error: error.message };
    }
  }

  private async handleTransferSuccess(transferData: any): Promise<WebhookResult> {
    try {
      logger.info("Transfer successful", {
        transferCode: transferData.transfer_code,
        amount: transferData.amount,
        recipient: transferData.recipient?.name
      });

      // Here we could update payout records or notify venue owners
      // For now, we'll just log the success

      return { success: true, processed: true };
    } catch (error) {
      logger.error("Failed to process transfer success", error);
      return { success: false, error: error.message };
    }
  }

  private async handleTransferFailed(transferData: any): Promise<WebhookResult> {
    try {
      logger.error("Transfer failed", {
        transferCode: transferData.transfer_code,
        amount: transferData.amount,
        recipient: transferData.recipient?.name,
        status: transferData.status
      });

      // Here we could retry the transfer or notify administrators
      // For now, we'll just log the failure

      return { success: true, processed: true };
    } catch (error) {
      logger.error("Failed to process transfer failure", error);
      return { success: false, error: error.message };
    }
  }

  // Manual verification method for testing
  async verifyPayment(reference: string): Promise<{
    success: boolean;
    verified?: boolean;
    amount?: number;
    error?: string;
  }> {
    try {
      const secretKey = Deno.env.get("PAYSTACK_SECRET_KEY");
      if (!secretKey) {
        return { success: false, error: "Paystack secret key not configured" };
      }

      const response = await fetch(`https://api.paystack.co/transaction/verify/${reference}`, {
        headers: {
          'Authorization': `Bearer ${secretKey}`,
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();

      if (!response.ok || !result.status) {
        return { success: false, error: result.message || 'Verification failed' };
      }

      return {
        success: true,
        verified: result.data.status === 'success',
        amount: result.data.amount
      };
    } catch (error) {
      logger.error("Payment verification failed", error, { reference });
      return { success: false, error: error.message };
    }
  }

  // Health check method
  async checkPaystackConnection(): Promise<{ healthy: boolean; error?: string }> {
    try {
      const secretKey = Deno.env.get("PAYSTACK_SECRET_KEY");
      if (!secretKey) {
        return { healthy: false, error: "Paystack secret key not configured" };
      }

      // Test connection by fetching banks (lightweight endpoint)
      const response = await fetch("https://api.paystack.co/bank", {
        headers: {
          'Authorization': `Bearer ${secretKey}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        return { healthy: true };
      } else {
        return { healthy: false, error: `HTTP ${response.status}` };
      }
    } catch (error) {
      return { healthy: false, error: error.message };
    }
  }
}

// Export singleton instance
export const paystackWebhookService = new PaystackWebhookService();

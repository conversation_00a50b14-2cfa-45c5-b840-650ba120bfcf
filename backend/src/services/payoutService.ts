// Payout service for processing venue owner payments
import { PocketBase, delay } from "../../deps.ts";
import { createLogger, PerformanceLogger } from "../utils/logger.ts";
import type { 
  Booking, 
  User,
  BookingWithRelations 
} from "../types/pocketbase.ts";
import type { 
  PaystackTransferRequest,
  PaystackResponse,
  PayoutResult
} from "../types/paystack.ts";

const logger = createLogger("PayoutService");

export interface PayoutSummary {
  totalProcessed: number;
  totalAmount: number;
  successful: number;
  failed: number;
  errors: string[];
}

class PayoutService {
  private pb: PocketBase | null = null;
  private paystackSecretKey: string;
  private isInitialized = false;
  private readonly maxRetries = 3;
  private readonly retryDelay = 2000; // 2 seconds

  constructor() {
    this.paystackSecretKey = Deno.env.get("PAYSTACK_SECRET_KEY") || "";
  }

  async initialize(): Promise<void> {
    const pbUrl = Deno.env.get("POCKETBASE_URL");
    const adminEmail = Deno.env.get("POCKETBASE_ADMIN_EMAIL");
    const adminPassword = Deno.env.get("POCKETBASE_ADMIN_PASSWORD");
    const nodeEnv = Deno.env.get("NODE_ENV") || "development";

    // Check if we have placeholder values or missing config
    const hasPlaceholderValues =
      !pbUrl || pbUrl.includes("localhost") ||
      !adminEmail || adminEmail.includes("admin@") ||
      !adminPassword || adminPassword.includes("your_") ||
      !this.paystackSecretKey || this.paystackSecretKey.includes("your_");

    if (hasPlaceholderValues) {
      // Always use mock service for invalid/placeholder values
      logger.warn("Payout service running in mock mode (configuration not available)", {
        environment: nodeEnv,
        hasPbUrl: !!pbUrl,
        hasAdminEmail: !!adminEmail,
        hasAdminPassword: !!adminPassword,
        hasPaystackKey: !!this.paystackSecretKey,
        isPlaceholder: hasPlaceholderValues
      });
      this.pb = null;
      this.isInitialized = true;
      return;
    }

    try {
      this.pb = new PocketBase(pbUrl);

      // Authenticate as admin
      await this.pb.admins.authWithPassword(adminEmail, adminPassword);

      this.isInitialized = true;
      logger.info("Payout service initialized");
    } catch (error) {
      // If PocketBase connection fails, fall back to mock mode
      logger.warn("Payout service falling back to mock mode due to connection error", {
        error: error instanceof Error ? error.message : String(error),
        pbUrl: pbUrl
      });
      this.pb = null;
      this.isInitialized = true;
    }
  }

  isHealthy(): boolean {
    return this.isInitialized;
  }

  async cleanup(): Promise<void> {
    if (this.pb) {
      this.pb.authStore.clear();
    }
    
    logger.info("Payout service cleaned up");
  }

  async processScheduledPayouts(): Promise<PayoutSummary> {
    const perfLogger = new PerformanceLogger("processScheduledPayouts");

    try {
      if (!this.isHealthy()) {
        throw new Error("Payout service not initialized");
      }

      // Mock mode - return empty summary
      if (!this.pb) {
        logger.info("Mock scheduled payout processing (no actual payouts)");
        const mockSummary = {
          totalProcessed: 0,
          totalAmount: 0,
          successful: 0,
          failed: 0,
          errors: []
        };
        perfLogger.end(mockSummary);
        return mockSummary;
      }

      logger.info("Starting scheduled payout processing");

      // Find completed bookings that need payouts
      const eligibleBookings = await this.findEligibleBookings();
      
      if (eligibleBookings.length === 0) {
        logger.info("No bookings eligible for payout");
        perfLogger.end({ totalProcessed: 0 });
        return {
          totalProcessed: 0,
          totalAmount: 0,
          successful: 0,
          failed: 0,
          errors: []
        };
      }

      logger.info("Processing payouts", { count: eligibleBookings.length });

      const summary: PayoutSummary = {
        totalProcessed: eligibleBookings.length,
        totalAmount: 0,
        successful: 0,
        failed: 0,
        errors: []
      };

      // Process each booking payout
      for (const booking of eligibleBookings) {
        try {
          const result = await this.processBookingPayout(booking);
          
          if (result.success) {
            summary.successful++;
            summary.totalAmount += booking.payout_amount;
            
            // Update booking status to completed
            await this.pb!.collection("bookings").update(booking.id, {
              status: "completed"
            });
            
            logger.info("Payout processed successfully", {
              bookingId: booking.id,
              amount: booking.payout_amount,
              transferCode: result.transferCode
            });
          } else {
            summary.failed++;
            summary.errors.push(`Booking ${booking.id}: ${result.error}`);
            
            logger.error("Payout failed", result.error, {
              bookingId: booking.id,
              amount: booking.payout_amount
            });
          }
        } catch (error) {
          summary.failed++;
          summary.errors.push(`Booking ${booking.id}: ${error.message}`);
          
          logger.error("Payout processing error", error, {
            bookingId: booking.id
          });
        }

        // Add delay between payouts to avoid rate limiting
        await delay(1000);
      }

      perfLogger.end(summary);
      logger.info("Scheduled payout processing completed", summary);
      
      return summary;
    } catch (error) {
      perfLogger.endWithError(error);
      logger.error("Failed to process scheduled payouts", error);
      throw error;
    }
  }

  private async findEligibleBookings(): Promise<BookingWithRelations[]> {
    if (!this.pb) return [];

    try {
      // Find bookings that are paid and past their end date
      const now = new Date().toISOString();
      
      const bookings = await this.pb.collection("bookings").getFullList({
        filter: `status = "paid" && end_date < "${now}"`,
        expand: "owner"
      }) as BookingWithRelations[];

      // Filter out bookings where owner doesn't have recipient code
      const eligibleBookings = bookings.filter(booking => {
        if (!booking.owner_expand?.paystack_recipient_code) {
          logger.warn("Owner missing Paystack recipient code", {
            bookingId: booking.id,
            ownerId: booking.owner,
            ownerEmail: booking.owner_expand?.email
          });
          return false;
        }
        return true;
      });

      logger.info("Found eligible bookings for payout", {
        total: bookings.length,
        eligible: eligibleBookings.length
      });

      return eligibleBookings;
    } catch (error) {
      logger.error("Failed to find eligible bookings", error);
      return [];
    }
  }

  private async processBookingPayout(booking: BookingWithRelations): Promise<PayoutResult> {
    if (!booking.owner_expand?.paystack_recipient_code) {
      return {
        success: false,
        error: "Owner missing Paystack recipient code",
        bookingId: booking.id
      };
    }

    try {
      // Create transfer request
      const transferRequest: PaystackTransferRequest = {
        source: "balance",
        amount: Math.round(booking.payout_amount * 100), // Convert to kobo
        recipient: booking.owner_expand.paystack_recipient_code,
        reason: `Payout for booking ${booking.id}`,
        currency: "NGN",
        reference: `payout_${booking.id}_${Date.now()}`
      };

      // Process transfer with retry logic
      const result = await this.processTransferWithRetry(transferRequest);
      
      if (result.success && result.data) {
        return {
          success: true,
          transferCode: result.data.transfer_code,
          amount: booking.payout_amount,
          recipient: booking.owner_expand.paystack_recipient_code,
          bookingId: booking.id
        };
      } else {
        return {
          success: false,
          error: result.error || "Transfer failed",
          bookingId: booking.id
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        bookingId: booking.id
      };
    }
  }

  private async processTransferWithRetry(
    transferRequest: PaystackTransferRequest,
    attempt = 1
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      const response = await fetch("https://api.paystack.co/transfer", {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${this.paystackSecretKey}`,
          "Content-Type": "application/json"
        },
        body: JSON.stringify(transferRequest)
      });

      const result: PaystackResponse = await response.json();

      if (!response.ok || !result.status) {
        throw new Error(result.message || "Transfer request failed");
      }

      logger.debug("Transfer initiated successfully", {
        transferCode: result.data?.transfer_code,
        amount: transferRequest.amount,
        recipient: transferRequest.recipient
      });

      return { success: true, data: result.data };
    } catch (error) {
      logger.warn(`Transfer attempt ${attempt} failed`, error, {
        recipient: transferRequest.recipient,
        amount: transferRequest.amount
      });

      if (attempt < this.maxRetries) {
        await delay(this.retryDelay * attempt); // Exponential backoff
        return this.processTransferWithRetry(transferRequest, attempt + 1);
      }

      return { success: false, error: error.message };
    }
  }

  // Manual payout method for individual bookings
  async processManualPayout(bookingId: string): Promise<PayoutResult> {
    try {
      if (!this.isHealthy()) {
        return {
          success: false,
          error: "Payout service not initialized",
          bookingId
        };
      }

      // Get booking with owner details
      const booking = await this.pb!.collection("bookings").getOne(bookingId, {
        expand: "owner"
      }) as BookingWithRelations;

      if (booking.status !== "paid") {
        return {
          success: false,
          error: "Booking is not in paid status",
          bookingId
        };
      }

      const now = new Date();
      const endDate = new Date(booking.end_date);
      
      if (endDate > now) {
        return {
          success: false,
          error: "Booking has not ended yet",
          bookingId
        };
      }

      const result = await this.processBookingPayout(booking);
      
      if (result.success) {
        // Update booking status to completed
        await this.pb!.collection("bookings").update(booking.id, {
          status: "completed"
        });
      }

      return result;
    } catch (error) {
      logger.error("Manual payout failed", error, { bookingId });
      return {
        success: false,
        error: error.message,
        bookingId
      };
    }
  }

  // Get payout statistics
  async getPayoutStats(): Promise<{
    pendingPayouts: number;
    totalPendingAmount: number;
    completedToday: number;
    totalCompletedToday: number;
  }> {
    try {
      if (!this.pb) {
        throw new Error("Service not initialized");
      }

      const now = new Date().toISOString();
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const todayStart = today.toISOString();

      // Get pending payouts
      const pendingBookings = await this.pb.collection("bookings").getFullList({
        filter: `status = "paid" && end_date < "${now}"`
      }) as Booking[];

      const pendingPayouts = pendingBookings.length;
      const totalPendingAmount = pendingBookings.reduce(
        (sum, booking) => sum + booking.payout_amount, 
        0
      );

      // Get completed payouts today
      const completedToday = await this.pb.collection("bookings").getFullList({
        filter: `status = "completed" && updated >= "${todayStart}"`
      }) as Booking[];

      const totalCompletedToday = completedToday.reduce(
        (sum, booking) => sum + booking.payout_amount, 
        0
      );

      return {
        pendingPayouts,
        totalPendingAmount,
        completedToday: completedToday.length,
        totalCompletedToday
      };
    } catch (error) {
      logger.error("Failed to get payout stats", error);
      throw error;
    }
  }

  // Health check method
  async checkPaystackConnection(): Promise<{ healthy: boolean; error?: string }> {
    try {
      // Test connection by fetching transfer recipients (lightweight endpoint)
      const response = await fetch("https://api.paystack.co/transferrecipient", {
        headers: {
          "Authorization": `Bearer ${this.paystackSecretKey}`,
          "Content-Type": "application/json"
        }
      });

      if (response.ok) {
        return { healthy: true };
      } else {
        return { healthy: false, error: `HTTP ${response.status}` };
      }
    } catch (error) {
      return { healthy: false, error: error.message };
    }
  }
}

// Export singleton instance
export const payoutService = new PayoutService();

#!/bin/bash

# Test script for invitation fix
echo "Testing invitation fix..."

BOOKING_ID="rb0bkv3yrrrt6y5"
API_BASE="https://trodoorentalsbe-production.up.railway.app"

echo "1. Testing debug endpoint..."
curl -s "${API_BASE}/api/internal/debug-booking/${BOOKING_ID}" | jq '.'

echo -e "\n2. Testing invitation endpoint..."
curl -X POST "${API_BASE}/api/internal/send-invitations" \
  -H "Content-Type: application/json" \
  -d '{
    "bookingId": "'${BOOKING_ID}'",
    "message": "Test invitation message",
    "emails": ["<EMAIL>"]
  }' | jq '.'

echo -e "\nTest completed."

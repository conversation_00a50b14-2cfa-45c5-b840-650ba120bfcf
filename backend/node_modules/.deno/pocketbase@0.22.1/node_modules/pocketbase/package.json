{"version": "0.22.1", "name": "pocketbase", "description": "PocketBase JavaScript SDK", "author": "<PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/pocketbase/js-sdk.git"}, "exports": {".": "./dist/pocketbase.es.mjs", "./cjs": "./dist/pocketbase.cjs.js", "./umd": "./dist/pocketbase.umd.js"}, "main": "./dist/pocketbase.es.mjs", "module": "./dist/pocketbase.es.mjs", "react-native": "./dist/pocketbase.es.js", "types": "./dist/pocketbase.es.d.mts", "keywords": ["pocketbase", "pocketbase-js", "js-sdk", "javascript-sdk", "pocketbase-sdk"], "prettier": {"tabWidth": 4, "printWidth": 90, "bracketSameLine": true}, "scripts": {"format": "npx prettier ./src ./tests --write", "build": "rm -rf dist && rollup -c", "dev": "rollup -c -w", "test": "vitest", "prepublishOnly": "npm run build"}, "devDependencies": {"@rollup/plugin-terser": "^0.4.3", "prettier": "3.2.4", "rollup": "^4.0.0", "rollup-plugin-ts": "^3.0.0", "typescript": "^5.1.6", "vitest": "^2.0.0"}}
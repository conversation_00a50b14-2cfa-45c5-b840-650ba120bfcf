# Docker Setup for Trodoo Backend

This document explains how to build and run the Trodoo backend service using Docker.

## 🐳 Quick Start

### Prerequisites
- Docker and Docker Compose installed
- Environment variables configured (see below)

### Build and Run
```bash
# Build and start all services
docker-compose up --build

# Run in background
docker-compose up -d --build

# View logs
docker-compose logs -f venue-services
```

## 📋 Environment Configuration

The service supports two modes:

### Development Mode (Mock Services)
All services will run in mock mode with placeholder environment variables. This is perfect for development and testing.

### Production Mode
Requires real API keys and configuration. Set these environment variables:

```bash
# Required for production
export POCKETBASE_URL="http://your-pocketbase-url:8090"
export POCKETBASE_ADMIN_EMAIL="<EMAIL>"
export POCKETBASE_ADMIN_PASSWORD="your_secure_password"
export RESEND_API_KEY="re_your_actual_resend_api_key"
export PAYSTACK_SECRET_KEY="sk_live_your_paystack_secret"
export PAYSTACK_WEBHOOK_SECRET="your_webhook_secret"

# Optional
export MEILISEARCH_API_KEY="your_meilisearch_key"
export FRONTEND_URL="https://yourdomain.com"
```

## 🔧 Docker Configuration

### Dockerfile Features
- **Base Image**: `denoland/deno:latest` (always uses latest stable Deno)
- **Dependency Caching**: Optimized layer caching for faster builds
- **Security**: Runs as non-root user
- **Health Checks**: Built-in health monitoring

### Services Included
- **venue-services**: Main Deno backend (Port 8000)
- **meilisearch**: Search engine (Port 7700)
- **redis**: Caching (Port 6379)

## 🚀 Testing the Build

### Test Dependencies
```bash
# Test if all imports work
deno run --allow-net --allow-env --allow-read docker-test.ts
```

### Test Docker Build
```bash
# Run the build test script
./build-test.sh
```

### Manual Testing
```bash
# Build image
docker build -t trodoo-backend .

# Run container
docker run -p 8000:8000 \
  -e NODE_ENV=development \
  trodoo-backend

# Test health endpoint
curl http://localhost:8000/health
```

## 📊 Service Health

The service provides a health endpoint at `/health` that returns:

```json
{
  "status": "healthy",
  "timestamp": "2025-06-23T09:39:11.633Z",
  "version": "1.0.0",
  "services": {
    "email": true,
    "meilisearch": true,
    "paystack": true
  }
}
```

## 🔍 Troubleshooting

### Common Issues

1. **Import Map Errors**: Fixed by using direct URLs in deps.ts
2. **PocketBase Version**: Updated to use npm package v0.26.0+
3. **Permission Errors**: Ensure Docker has proper permissions

### Debug Commands
```bash
# Check container logs
docker-compose logs venue-services

# Enter container shell
docker-compose exec venue-services sh

# Test dependencies inside container
docker-compose exec venue-services deno cache deps.ts
```

## 🎯 Production Deployment

For production deployment:

1. Set all required environment variables
2. Use proper secrets management
3. Configure reverse proxy (nginx/traefik)
4. Set up monitoring and logging
5. Configure backup strategies

### Example Production docker-compose.override.yml
```yaml
version: '3.8'
services:
  venue-services:
    environment:
      - NODE_ENV=production
      - LOG_LEVEL=WARN
    restart: always
```

## 📝 Notes

- The service automatically detects mock vs production mode
- All services gracefully handle missing configuration
- Health checks ensure service reliability
- Optimized for both development and production use

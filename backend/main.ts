// Main server entry point for Deno Venue Services
import { 
  Application, 
  Router, 
  oakCors, 
  load,
  Status,
  cron
} from "./deps.ts";

import { createLogger, logRequest } from "./src/utils/logger.ts";
import { emailService } from "./src/services/emailService.ts";
import { notificationService } from "./src/services/notificationService.ts";
import { meilisearchSyncService } from "./src/services/meilisearchSyncService.ts";
import { paystackWebhookService } from "./src/services/paystackWebhookService.ts";
import { payoutService } from "./src/services/payoutService.ts";

// Load environment variables (only in development)
const nodeEnv = Deno.env.get("NODE_ENV") || "development";
if (nodeEnv === "development") {
  try {
    // Only try to load .env file in development
    await load({ allowEmptyValues: true, examplePath: null });
    console.log("✅ Loaded .env file for development");
  } catch {
    // Ignore if .env file doesn't exist in development
    console.log("ℹ️  No .env file found, using system environment variables");
  }
} else {
  // In production (Railway), we rely entirely on system environment variables
  console.log("ℹ️  Production mode: using system environment variables");
}

const logger = createLogger("Main");
const PORT = parseInt(Deno.env.get("PORT") || "8000");
const NODE_ENV = Deno.env.get("NODE_ENV") || "development";

// Create Oak application
const app = new Application();
const router = new Router();

// Error handling middleware
app.use(async (ctx, next) => {
  try {
    await next();
  } catch (error) {
    logger.error("Unhandled error", error, {
      url: ctx.request.url.toString(),
      method: ctx.request.method,
    });
    
    ctx.response.status = Status.InternalServerError;
    ctx.response.body = {
      error: "Internal server error",
      message: NODE_ENV === "development" ? error.message : "Something went wrong",
    };
  }
});

// Request logging middleware
app.use(async (ctx, next) => {
  const start = Date.now();
  await next();
  const duration = Date.now() - start;
  
  logRequest(
    "HTTP",
    ctx.request.method,
    ctx.request.url.pathname,
    ctx.response.status
  );
  
  logger.debug("Request completed", {
    method: ctx.request.method,
    url: ctx.request.url.pathname,
    status: ctx.response.status,
    duration: `${duration}ms`,
  });
});

// CORS middleware
app.use(oakCors({
  origin: [
    Deno.env.get("FRONTEND_URL") || "http://localhost:4321",
    "http://localhost:3000", // For development
  ],
  credentials: true,
}));

// Health check endpoint
router.get("/health", (ctx) => {
  const nodeEnv = Deno.env.get("NODE_ENV") || "development";
  const hasRealCredentials =
    Deno.env.get("RESEND_API_KEY") && !Deno.env.get("RESEND_API_KEY")?.startsWith("re_your_") &&
    Deno.env.get("POCKETBASE_ADMIN_PASSWORD") && !Deno.env.get("POCKETBASE_ADMIN_PASSWORD")?.includes("your_") &&
    Deno.env.get("PAYSTACK_SECRET_KEY") && !Deno.env.get("PAYSTACK_SECRET_KEY")?.includes("your_");

  ctx.response.body = {
    status: "healthy",
    timestamp: new Date().toISOString(),
    version: "1.0.0",
    environment: nodeEnv,
    mode: hasRealCredentials ? "production" : "mock",
    services: {
      email: emailService.isHealthy(),
      notification: notificationService.isHealthy(),
      meilisearch: meilisearchSyncService.isHealthy(),
      paystack: paystackWebhookService.isHealthy(),
      payout: payoutService.isHealthy(),
    },
    message: hasRealCredentials
      ? "All services running with real credentials"
      : "Services running in mock mode - configure environment variables for production",
  };
});

// Paystack webhook endpoint
router.post("/webhooks/paystack", async (ctx) => {
  try {
    const body = await ctx.request.body({ type: "text" }).value;
    const signature = ctx.request.headers.get("x-paystack-signature");
    
    if (!signature) {
      ctx.response.status = Status.BadRequest;
      ctx.response.body = { error: "Missing signature" };
      return;
    }
    
    const result = await paystackWebhookService.handleWebhook(body, signature);
    
    if (result.success) {
      ctx.response.status = Status.OK;
      ctx.response.body = { message: "Webhook processed successfully" };
    } else {
      ctx.response.status = Status.BadRequest;
      ctx.response.body = { error: result.error };
    }
  } catch (error) {
    logger.error("Webhook processing failed", error);
    ctx.response.status = Status.InternalServerError;
    ctx.response.body = { error: "Webhook processing failed" };
  }
});

// Debug endpoint for booking data
router.get("/api/internal/debug-booking/:bookingId", async (ctx) => {
  try {
    const bookingId = ctx.params.bookingId;

    if (!bookingId) {
      ctx.response.status = Status.BadRequest;
      ctx.response.body = { error: "Missing booking ID" };
      return;
    }

    // Check notification service status
    const isServiceHealthy = notificationService.isHealthy();

    if (!isServiceHealthy) {
      ctx.response.status = Status.ServiceUnavailable;
      ctx.response.body = {
        error: "Notification service not available",
        serviceStatus: "unhealthy",
        suggestion: "Check environment variables and PocketBase connection"
      };
      return;
    }

    // Try to get booking data (this will help debug the issue)
    const debugResult = await notificationService.debugBookingData(bookingId);

    ctx.response.status = Status.OK;
    ctx.response.body = debugResult;
  } catch (error) {
    logger.error("Error in debug-booking endpoint", error);
    ctx.response.status = Status.InternalServerError;
    ctx.response.body = {
      error: "Internal server error",
      details: error instanceof Error ? error.message : String(error)
    };
  }
});

// Debug endpoint for individual records
router.get("/api/internal/debug-records/:bookingId", async (ctx) => {
  try {
    const bookingId = ctx.params.bookingId;

    if (!bookingId) {
      ctx.response.status = Status.BadRequest;
      ctx.response.body = { error: "Missing booking ID" };
      return;
    }

    if (!notificationService.isHealthy()) {
      ctx.response.status = Status.ServiceUnavailable;
      ctx.response.body = { error: "Notification service not available" };
      return;
    }

    const result = await notificationService.debugIndividualRecords(bookingId);
    ctx.response.status = Status.OK;
    ctx.response.body = result;
  } catch (error) {
    logger.error("Error in debug-records endpoint", error);
    ctx.response.status = Status.InternalServerError;
    ctx.response.body = {
      error: "Internal server error",
      details: error instanceof Error ? error.message : String(error)
    };
  }
});

// Guest invitation endpoint
router.post("/api/internal/send-invitations", async (ctx) => {
  try {
    const body = await ctx.request.body({ type: "json" }).value;
    const { bookingId, message, emails } = body;

    logger.info("Invitation request received", { bookingId, emailCount: emails?.length });

    if (!bookingId || !emails || !Array.isArray(emails)) {
      logger.warn("Invalid invitation request", { bookingId, emails });
      ctx.response.status = Status.BadRequest;
      ctx.response.body = { error: "Missing required fields" };
      return;
    }

    // Check if notification service is healthy
    if (!notificationService.isHealthy()) {
      logger.error("Notification service is not healthy");
      ctx.response.status = Status.ServiceUnavailable;
      ctx.response.body = {
        error: "Notification service unavailable",
        suggestion: "Service may be in mock mode - check environment variables"
      };
      return;
    }

    logger.info("Calling sendGuestInvitations", { bookingId });
    const result = await notificationService.sendGuestInvitations({
      bookingId,
      message: message || "",
      emails,
    });

    logger.info("sendGuestInvitations result", { success: result.success, error: result.error });

    if (result.success) {
      ctx.response.status = Status.OK;
      ctx.response.body = {
        message: "Invitations sent successfully",
        sentCount: result.sentCount,
      };
    } else {
      ctx.response.status = Status.BadRequest;
      ctx.response.body = { error: result.error };
    }
  } catch (error) {
    logger.error("Invitation sending failed", error);
    ctx.response.status = Status.InternalServerError;
    ctx.response.body = { error: "Failed to send invitations" };
  }
});

// API status endpoint
router.get("/api/status", (ctx) => {
  ctx.response.body = {
    status: "running",
    environment: NODE_ENV,
    timestamp: new Date().toISOString(),
    uptime: performance.now(),
  };
});

// Add routes to app
app.use(router.routes());
app.use(router.allowedMethods());

// 404 handler
app.use((ctx) => {
  ctx.response.status = Status.NotFound;
  ctx.response.body = { error: "Not found" };
});

// Initialize services
async function initializeServices() {
  logger.info("Initializing services...");

  const initializationErrors: string[] = [];

  try {
    // Initialize email service
    try {
      await emailService.initialize();
      logger.info("Email service initialized");
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.warn("Email service initialization failed, continuing in mock mode", { error: errorMessage });
      initializationErrors.push(`Email service: ${errorMessage}`);
    }

    // Initialize MeiliSearch sync service
    try {
      await meilisearchSyncService.initialize();
      logger.info("MeiliSearch sync service initialized");
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.warn("MeiliSearch sync service initialization failed, continuing in mock mode", { error: errorMessage });
      initializationErrors.push(`MeiliSearch service: ${errorMessage}`);
    }

    // Initialize notification service
    try {
      await notificationService.initialize();
      logger.info("Notification service initialized");
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.warn("Notification service initialization failed, continuing in mock mode", { error: errorMessage });
      initializationErrors.push(`Notification service: ${errorMessage}`);
    }

    // Initialize Paystack webhook service
    try {
      await paystackWebhookService.initialize();
      logger.info("Paystack webhook service initialized");
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.warn("Paystack webhook service initialization failed, continuing in mock mode", { error: errorMessage });
      initializationErrors.push(`Paystack service: ${errorMessage}`);
    }

    // Initialize payout service
    try {
      await payoutService.initialize();
      logger.info("Payout service initialized");
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.warn("Payout service initialization failed, continuing in mock mode", { error: errorMessage });
      initializationErrors.push(`Payout service: ${errorMessage}`);
    }

    if (initializationErrors.length > 0) {
      logger.warn("Some services failed to initialize but server will continue in mock mode", {
        errors: initializationErrors,
        environment: NODE_ENV
      });
    } else {
      logger.info("All services initialized successfully");
    }
  } catch (error) {
    logger.critical("Critical failure during service initialization", error);
    Deno.exit(1);
  }
}

// Setup cron jobs
function setupCronJobs() {
  const enableCronJobs = Deno.env.get("ENABLE_CRON_JOBS") === "true";
  
  if (!enableCronJobs) {
    logger.info("Cron jobs disabled");
    return;
  }
  
  logger.info("Setting up cron jobs...");
  
  // Daily payout processing (2 AM)
  const payoutSchedule = Deno.env.get("PAYOUT_CRON_SCHEDULE") || "0 2 * * *";
  cron(payoutSchedule, async () => {
    logger.info("Running scheduled payout processing");
    await payoutService.processScheduledPayouts();
  });
  
  // Hourly booking reminders
  const reminderSchedule = Deno.env.get("REMINDER_CRON_SCHEDULE") || "0 * * * *";
  cron(reminderSchedule, async () => {
    logger.info("Running scheduled booking reminders");
    await notificationService.sendBookingReminders();
  });
  
  // Check-in reminders (every 15 minutes)
  const checkinSchedule = Deno.env.get("CHECKIN_REMINDER_CRON_SCHEDULE") || "*/15 * * * *";
  cron(checkinSchedule, async () => {
    logger.info("Running check-in reminders");
    await notificationService.sendCheckinReminders();
  });
  
  // Daily review prompts (10 AM)
  const reviewSchedule = Deno.env.get("REVIEW_PROMPT_CRON_SCHEDULE") || "0 10 * * *";
  cron(reviewSchedule, async () => {
    logger.info("Running review prompts");
    await notificationService.sendReviewPrompts();
  });
  
  logger.info("Cron jobs configured successfully");
}

// Graceful shutdown
function setupGracefulShutdown() {
  const shutdown = async (signal: string) => {
    logger.info(`Received ${signal}, shutting down gracefully...`);
    
    try {
      // Cleanup services
      await notificationService.cleanup();
      await meilisearchSyncService.cleanup();
      await payoutService.cleanup();
      
      logger.info("Services cleaned up successfully");
      Deno.exit(0);
    } catch (error) {
      logger.error("Error during shutdown", error);
      Deno.exit(1);
    }
  };
  
  // Handle shutdown signals
  Deno.addSignalListener("SIGINT", () => shutdown("SIGINT"));
  Deno.addSignalListener("SIGTERM", () => shutdown("SIGTERM"));
}

// Start server
async function startServer() {
  try {
    // Initialize all services
    await initializeServices();
    
    // Setup cron jobs
    setupCronJobs();
    
    // Setup graceful shutdown
    setupGracefulShutdown();
    
    // Start the server
    logger.info(`Starting server on port ${PORT} in ${NODE_ENV} mode`);
    await app.listen({ port: PORT });
  } catch (error) {
    logger.critical("Failed to start server", error);
    Deno.exit(1);
  }
}

// Start the application
if (import.meta.main) {
  await startServer();
}

#!/usr/bin/env -S deno run --allow-net --allow-env --allow-read

// Test script to simulate Railway deployment environment exactly
// This removes the .env.example file temporarily to test the exact Railway scenario

console.log("🚂 Railway Deployment Simulation Test");
console.log("=====================================");

// Set Railway-like environment variables (minimal set)
Deno.env.set("NODE_ENV", "production");
Deno.env.set("PORT", "8000");

// Set only the basic required variables that Railway might have
Deno.env.set("POCKETBASE_URL", "https://trodoorentals.pockethost.io");
Deno.env.set("POCKETBASE_ADMIN_EMAIL", "<EMAIL>");
Deno.env.set("POCKETBASE_ADMIN_PASSWORD", "your_admin_password");
Deno.env.set("RESEND_API_KEY", "re_your_resend_api_key");
Deno.env.set("PAYSTACK_SECRET_KEY", "sk_test_your_paystack_secret_key");
Deno.env.set("PAYSTACK_WEBHOOK_SECRET", "your_webhook_secret");

// Note: We're NOT setting the variables that were causing the error:
// MEILISEARCH_HOST, MEILISEARCH_API_KEY, LOG_LEVEL, RATE_LIMIT_WINDOW_MS, 
// RATE_LIMIT_MAX_REQUESTS, ENABLE_CRON_JOBS, etc.

const startTime = Date.now();

try {
  console.log("📦 Testing main.ts import (this should work now)...");
  
  // This should now work without the MissingEnvVarsError
  const mainModule = await import("./main.ts");
  
  console.log("✅ main.ts imported successfully!");
  console.log("✅ No MissingEnvVarsError thrown!");
  
  // Test that services can be imported and initialized
  console.log("\n🔧 Testing service initialization...");
  
  const { emailService } = await import("./src/services/emailService.ts");
  const { notificationService } = await import("./src/services/notificationService.ts");
  const { meilisearchSyncService } = await import("./src/services/meilisearchSyncService.ts");
  const { paystackWebhookService } = await import("./src/services/paystackWebhookService.ts");
  const { payoutService } = await import("./src/services/payoutService.ts");
  
  // Initialize services
  await emailService.initialize();
  await notificationService.initialize();
  await meilisearchSyncService.initialize();
  await paystackWebhookService.initialize();
  await payoutService.initialize();
  
  console.log("✅ All services initialized successfully");
  
  // Check health status
  console.log("\n🏥 Service Health Status:");
  console.log(`   Email: ${emailService.isHealthy()}`);
  console.log(`   Notification: ${notificationService.isHealthy()}`);
  console.log(`   MeiliSearch: ${meilisearchSyncService.isHealthy()}`);
  console.log(`   Paystack: ${paystackWebhookService.isHealthy()}`);
  console.log(`   Payout: ${payoutService.isHealthy()}`);
  
  const endTime = Date.now();
  const duration = ((endTime - startTime) / 1000).toFixed(2);
  
  console.log("\n🎉 Railway Simulation Test Results:");
  console.log(`   ✅ No MissingEnvVarsError`);
  console.log(`   ✅ Application imports successfully`);
  console.log(`   ✅ All services initialize in mock mode`);
  console.log(`   ✅ Ready for Railway deployment`);
  console.log(`   ⏱️  Total time: ${duration}s`);
  
  console.log("\n🚂 Railway Deployment Status: READY ✅");
  console.log("   The application will now start successfully on Railway!");
  
} catch (error) {
  console.error("❌ Railway simulation test failed:");
  console.error("Error:", error.message);
  console.error("\nThis indicates the Railway deployment would still fail.");
  console.error("Error details:", error);
  Deno.exit(1);
}

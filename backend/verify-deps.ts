#!/usr/bin/env -S deno run --allow-net --allow-env --allow-read

// Verify that all dependencies can be imported and cached successfully
// This simulates what happens during Docker build

console.log("🔍 Verifying Dependencies for Docker Build");
console.log("==========================================");

const startTime = Date.now();

try {
  console.log("📦 Caching deps.ts...");
  
  // This is equivalent to what Docker does: deno cache deps.ts
  const cacheProcess = new Deno.Command("deno", {
    args: ["cache", "deps.ts"],
    stdout: "piped",
    stderr: "piped"
  });
  
  const { code, stdout, stderr } = await cacheProcess.output();
  
  if (code === 0) {
    console.log("✅ deps.ts cached successfully");
    
    // Test importing key dependencies
    console.log("🧪 Testing key imports...");
    
    const { PocketBase } = await import("./deps.ts");
    console.log("✅ PocketBase imported");
    
    const { Application } = await import("./deps.ts");
    console.log("✅ Oak Application imported");
    
    const { delay } = await import("./deps.ts");
    console.log("✅ Delay utility imported");
    
    const { z } = await import("./deps.ts");
    console.log("✅ Zod validation imported");
    
    // Test creating instances
    const pb = new PocketBase("http://test.example.com");
    console.log("✅ PocketBase instance created");
    
    const app = new Application();
    console.log("✅ Oak Application instance created");
    
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);
    
    console.log("");
    console.log(`🎉 All dependencies verified successfully in ${duration}s`);
    console.log("🐳 Docker build should work without issues!");
    
  } else {
    console.error("❌ Failed to cache deps.ts");
    console.error("STDOUT:", new TextDecoder().decode(stdout));
    console.error("STDERR:", new TextDecoder().decode(stderr));
    Deno.exit(1);
  }
  
} catch (error) {
  console.error("❌ Verification failed:", error.message);
  Deno.exit(1);
}

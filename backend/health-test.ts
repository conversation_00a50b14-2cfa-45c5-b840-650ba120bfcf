#!/usr/bin/env -S deno run --allow-net --allow-env --allow-read

// Test the health endpoint with placeholder values

console.log("🏥 Testing Health Endpoint");
console.log("==========================");

// Set placeholder environment variables
Deno.env.set("NODE_ENV", "production");
Deno.env.set("PORT", "8001");
Deno.env.set("POCKETBASE_URL", "https://trodoorentals.pockethost.io");
Deno.env.set("POCKETBASE_ADMIN_EMAIL", "<EMAIL>");
Deno.env.set("POCKETBASE_ADMIN_PASSWORD", "your_admin_password");
Deno.env.set("RESEND_API_KEY", "re_your_resend_api_key");
Deno.env.set("PAYSTACK_SECRET_KEY", "sk_test_your_paystack_secret_key");
Deno.env.set("PAYSTACK_WEBHOOK_SECRET", "your_webhook_secret");
Deno.env.set("FRONTEND_URL", "http://localhost:4321");

try {
  // Import and start the application
  const { Application, Router } = await import("./deps.ts");
  const { createLogger } = await import("./src/utils/logger.ts");
  const { emailService } = await import("./src/services/emailService.ts");
  const { notificationService } = await import("./src/services/notificationService.ts");
  const { meilisearchSyncService } = await import("./src/services/meilisearchSyncService.ts");
  const { paystackWebhookService } = await import("./src/services/paystackWebhookService.ts");
  const { payoutService } = await import("./src/services/payoutService.ts");

  const logger = createLogger("HealthTest");
  const PORT = 8001;

  // Initialize services
  console.log("🔧 Initializing services...");
  await emailService.initialize();
  await notificationService.initialize();
  await meilisearchSyncService.initialize();
  await paystackWebhookService.initialize();
  await payoutService.initialize();
  console.log("✅ Services initialized");

  // Create a simple app with health endpoint
  const app = new Application();
  const router = new Router();

  // Health check endpoint (copied from main.ts)
  router.get("/health", (ctx) => {
    const nodeEnv = Deno.env.get("NODE_ENV") || "development";
    const hasRealCredentials = 
      Deno.env.get("RESEND_API_KEY") && !Deno.env.get("RESEND_API_KEY")?.startsWith("re_your_") &&
      Deno.env.get("POCKETBASE_ADMIN_PASSWORD") && !Deno.env.get("POCKETBASE_ADMIN_PASSWORD")?.includes("your_") &&
      Deno.env.get("PAYSTACK_SECRET_KEY") && !Deno.env.get("PAYSTACK_SECRET_KEY")?.includes("your_");

    ctx.response.body = {
      status: "healthy",
      timestamp: new Date().toISOString(),
      version: "1.0.0",
      environment: nodeEnv,
      mode: hasRealCredentials ? "production" : "mock",
      services: {
        email: emailService.isHealthy(),
        notification: notificationService.isHealthy(),
        meilisearch: meilisearchSyncService.isHealthy(),
        paystack: paystackWebhookService.isHealthy(),
        payout: payoutService.isHealthy(),
      },
      message: hasRealCredentials 
        ? "All services running with real credentials" 
        : "Services running in mock mode - configure environment variables for production",
    };
  });

  app.use(router.routes());
  app.use(router.allowedMethods());

  console.log(`🚀 Starting test server on port ${PORT}...`);
  
  // Start server in background
  const serverPromise = app.listen({ port: PORT });
  
  // Wait a moment for server to start
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Test the health endpoint
  console.log("🔍 Testing health endpoint...");
  const response = await fetch(`http://localhost:${PORT}/health`);
  
  if (response.ok) {
    const healthData = await response.json();
    console.log("✅ Health endpoint responding!");
    console.log("📊 Health data:", JSON.stringify(healthData, null, 2));
  } else {
    console.error(`❌ Health endpoint failed: HTTP ${response.status}`);
  }
  
  console.log("\n🎉 Health endpoint test completed successfully!");
  console.log("💡 This confirms the application will start properly on Railway");
  
  Deno.exit(0);
  
} catch (error) {
  console.error("❌ Health test failed:", error.message);
  console.error("Error details:", error);
  Deno.exit(1);
}

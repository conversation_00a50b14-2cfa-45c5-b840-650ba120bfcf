#!/usr/bin/env -S deno run --allow-net --allow-env --allow-read

// Simple test to verify Docker build dependencies work
import { PocketBase } from "./deps.ts";

console.log("🐳 Docker Build Test");
console.log("==================");

try {
  // Test PocketBase import
  const pb = new PocketBase("http://test.example.com");
  console.log("✅ PocketBase import: Working");
  
  // Test other critical imports
  const { Application } = await import("./deps.ts");
  console.log("✅ Oak Application import: Working");
  
  const { emailService } = await import("./src/services/emailService.ts");
  console.log("✅ Email Service import: Working");
  
  console.log("");
  console.log("🎯 All critical imports successful!");
  console.log("Docker build should work correctly.");
  
} catch (error) {
  console.error("❌ Import failed:", error.message);
  Deno.exit(1);
}

#!/usr/bin/env -S deno run --allow-net --allow-env

// Test script to verify PocketBase connection logic
import { PocketBase } from "./deps.ts";

console.log("🧪 Testing PocketBase Connection Logic");
console.log("=====================================");

// Test the same logic as the notification service
const pbUrl = Deno.env.get("POCKETBASE_URL");
const adminEmail = Deno.env.get("POCKETBASE_ADMIN_EMAIL");
const adminPassword = Deno.env.get("POCKETBASE_ADMIN_PASSWORD");
const nodeEnv = Deno.env.get("NODE_ENV") || "development";

console.log("\n1. Environment Variables:");
console.log(`   NODE_ENV: ${nodeEnv}`);
console.log(`   POCKETBASE_URL: ${pbUrl ? `${pbUrl.substring(0, 30)}...` : "NOT_SET"}`);
console.log(`   POCKETBASE_ADMIN_EMAIL: ${adminEmail ? `${adminEmail.substring(0, 10)}...` : "NOT_SET"}`);
console.log(`   POCKETBASE_ADMIN_PASSWORD: ${adminPassword ? `[${adminPassword.length} chars]` : "NOT_SET"}`);

// Check the new logic
const hasPlaceholderValues =
  !pbUrl || pbUrl === "http://localhost:8090" ||
  !adminEmail || adminEmail === "<EMAIL>" ||
  !adminPassword || adminPassword.includes("your_");

console.log("\n2. Placeholder Value Checks:");
console.log(`   Has PB URL: ${!!pbUrl}`);
console.log(`   PB URL is localhost: ${pbUrl === "http://localhost:8090"}`);
console.log(`   Has admin email: ${!!adminEmail}`);
console.log(`   Admin email is default: ${adminEmail === "<EMAIL>"}`);
console.log(`   Has admin password: ${!!adminPassword}`);
console.log(`   Admin password contains 'your_': ${adminPassword?.includes("your_") || false}`);
console.log(`   Has placeholder values: ${hasPlaceholderValues}`);

if (hasPlaceholderValues) {
  console.log("\n❌ Would run in MOCK mode due to placeholder values");
  Deno.exit(1);
}

console.log("\n✅ Would run in PRODUCTION mode");

// Test actual connection
if (pbUrl && adminEmail && adminPassword) {
  console.log("\n3. Testing actual PocketBase connection...");
  
  try {
    const pb = new PocketBase(pbUrl);
    await pb.admins.authWithPassword(adminEmail, adminPassword);
    console.log("✅ PocketBase connection successful!");
    
    // Test a simple query
    try {
      const collections = await pb.collections.getFullList();
      console.log(`✅ Found ${collections.length} collections`);
    } catch (error) {
      console.log(`⚠️  Could not list collections: ${error.message}`);
    }
    
  } catch (error) {
    console.log(`❌ PocketBase connection failed: ${error.message}`);
    Deno.exit(1);
  }
}

console.log("\n🎉 All tests passed!");

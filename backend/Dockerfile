# Dockerfile for Deno Venue Services
FROM denoland/deno:latest

# Set working directory
WORKDIR /app

# Copy dependency file first for better caching
COPY deps.ts .

# Cache dependencies
RUN deno cache deps.ts

# Copy configuration file
COPY deno.json .

# Copy source code
COPY . .

# Cache the main application
RUN deno cache main.ts

# Create non-root user for security
RUN groupadd -r denouser && useradd -r -g denouser denouser
RUN chown -R denouser:denouser /app
USER denouser

# Expose port
EXPOSE 8000

# # Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD deno run --allow-net --allow-env healthcheck.ts

# Run the application
CMD ["deno", "run", "--allow-net", "--allow-env", "--allow-read", "main.ts"]
